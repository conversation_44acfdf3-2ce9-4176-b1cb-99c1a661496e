<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteTargetsManager">
    <targets>
      <target name="WSL" type="wsl" uuid="17fbdf46-7bd6-4a9a-9528-089cddfc4df7">
        <config>
          <option name="distributionMsId" value="Ubuntu" />
        </config>
        <ContributedStateBase type="JavaLanguageRuntime">
          <config>
            <option name="homePath" value="/home/<USER>/.jdks/openjdk-23.0.1" />
          </config>
        </ContributedStateBase>
        <ContributedStateBase type="MavenRuntime">
          <config>
            <option name="homePath" value="/usr/share/maven" />
            <option name="versionString" value="3.6.3" />
          </config>
        </ContributedStateBase>
      </target>
    </targets>
  </component>
</project>